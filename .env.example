# Environment Configuration
NODE_ENV=development

# Frontend Configuration
NEXT_PUBLIC_SOLANA_NETWORK=devnet
NEXT_PUBLIC_RPC_ENDPOINT=https://api.devnet.solana.com

# Backend Configuration
PORT=3001
FRONTEND_URL=http://localhost:3000

# Blockchain Configuration
SOLANA_RPC_URL=https://api.devnet.solana.com
WALLET_PATH=./wallet.json

# Program IDs (replace with actual deployed program IDs)
ENERGY_CREDIT_PROGRAM_ID=EnergyCredit11111111111111111111111111111111
ENERGY_MARKET_PROGRAM_ID=EnergyMarket1111111111111111111111111111111
ENERGY_ORACLE_PROGRAM_ID=EnergyOracle111111111111111111111111111111

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/maschain_energy
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# IoT Configuration
MQTT_BROKER_HOST=localhost
MQTT_BROKER_PORT=1883
MQTT_USERNAME=
MQTT_PASSWORD=

# External APIs
WEATHER_API_KEY=your-weather-api-key
GRID_API_ENDPOINT=https://api.grid-provider.com
GRID_API_KEY=your-grid-api-key

# Monitoring
LOG_LEVEL=info
SENTRY_DSN=

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
