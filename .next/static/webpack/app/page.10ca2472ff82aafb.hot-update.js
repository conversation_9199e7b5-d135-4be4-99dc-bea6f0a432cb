"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BoltIcon,ChartBarIcon,CircleStackIcon,CurrencyDollarIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BoltIcon,ChartBarIcon,CircleStackIcon,CurrencyDollarIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BoltIcon,ChartBarIcon,CircleStackIcon,CurrencyDollarIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BoltIcon,ChartBarIcon,CircleStackIcon,CurrencyDollarIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BoltIcon,ChartBarIcon,CircleStackIcon,CurrencyDollarIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SunIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BoltIcon,ChartBarIcon,CircleStackIcon,CurrencyDollarIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _components_TradingDashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/TradingDashboard */ \"(app-pages-browser)/./src/components/TradingDashboard.tsx\");\n/* harmony import */ var _components_EnergyStats__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/EnergyStats */ \"(app-pages-browser)/./src/components/EnergyStats.tsx\");\n/* harmony import */ var _components_MarketOverview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/MarketOverview */ \"(app-pages-browser)/./src/components/MarketOverview.tsx\");\n/* harmony import */ var _store_energyStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/energyStore */ \"(app-pages-browser)/./src/store/energyStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dashboard\");\n    const { energyData, marketData, fetchEnergyData, fetchMarketData } = (0,_store_energyStore__WEBPACK_IMPORTED_MODULE_5__.useEnergyStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate wallet connection for demo\n        setConnected(true);\n        fetchEnergyData();\n        fetchMarketData();\n    }, [\n        fetchEnergyData,\n        fetchMarketData\n    ]);\n    const tabs = [\n        {\n            id: \"dashboard\",\n            name: \"Dashboard\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: \"trading\",\n            name: \"Trading\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: \"analytics\",\n            name: \"Analytics\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        }\n    ];\n    if (!connected) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-green-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-screen p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-20 w-20 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 h-6 w-6 bg-energy-production rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Maschain Energy Trading Platform\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-8\",\n                            children: \"Trade renewable energy on the blockchain. Connect your wallet to get started.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-12 w-12 text-energy-production mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Solar Energy\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Trade excess solar energy with your neighbors\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-12 w-12 text-energy-trading mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Energy Storage\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Store and trade energy when prices are optimal\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 text-primary-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Real-time Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Monitor your energy production and consumption\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setConnected(true),\n                                className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg\",\n                                children: \"Connect Demo Wallet\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BoltIcon_ChartBarIcon_CircleStackIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-primary-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"Maschain Energy Trading\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Demo Wallet: 11111111...11111111\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setConnected(false),\n                                        className: \"bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg text-sm\",\n                                        children: \"Disconnect\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex space-x-8\",\n                        children: tabs.map((tab)=>{\n                            const Icon = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"flex items-center py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 19\n                                    }, this),\n                                    tab.name\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    activeTab === \"dashboard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnergyStats__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarketOverview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"trading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TradingDashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 37\n                    }, this),\n                    activeTab === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-6\",\n                                children: \"Energy Analytics\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Detailed analytics and insights coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/app/page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"VKHrKslD+wVp6kFMzLqaRs1ijtE=\", false, function() {\n    return [\n        _store_energyStore__WEBPACK_IMPORTED_MODULE_5__.useEnergyStore\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CircleStackIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125\"\n    }));\n}\n_c = CircleStackIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CircleStackIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CircleStackIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0NpcmNsZVN0YWNrSWNvbi5qcyIsIm1hcHBpbmdzIjoiOztBQUErQjtBQUMvQixTQUFTQyxnQkFBZ0IsS0FJeEIsRUFBRUMsTUFBTTtRQUpnQixFQUN2QkMsS0FBSyxFQUNMQyxPQUFPLEVBQ1AsR0FBR0MsT0FDSixHQUp3QjtJQUt2QixPQUFPLFdBQVcsR0FBRUwsZ0RBQW1CLENBQUMsT0FBT08sT0FBT0MsTUFBTSxDQUFDO1FBQzNEQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLFFBQVE7UUFDUixlQUFlO1FBQ2YsYUFBYTtRQUNiQyxLQUFLWjtRQUNMLG1CQUFtQkU7SUFDckIsR0FBR0MsUUFBUUYsUUFBUSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFNBQVM7UUFDM0RlLElBQUlYO0lBQ04sR0FBR0QsU0FBUyxNQUFNLFdBQVcsR0FBRUgsZ0RBQW1CLENBQUMsUUFBUTtRQUN6RGdCLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxHQUFHO0lBQ0w7QUFDRjtLQXRCU2pCO0FBdUJULE1BQU1rQixhQUFhLFdBQVcsR0FBR25CLDZDQUFnQixDQUFDQzs7QUFDbEQsK0RBQWVrQixVQUFVQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0NpcmNsZVN0YWNrSWNvbi5qcz85MjMyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gQ2lyY2xlU3RhY2tJY29uKHtcbiAgdGl0bGUsXG4gIHRpdGxlSWQsXG4gIC4uLnByb3BzXG59LCBzdmdSZWYpIHtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwic3ZnXCIsIE9iamVjdC5hc3NpZ24oe1xuICAgIHhtbG5zOiBcImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIsXG4gICAgZmlsbDogXCJub25lXCIsXG4gICAgdmlld0JveDogXCIwIDAgMjQgMjRcIixcbiAgICBzdHJva2VXaWR0aDogMS41LFxuICAgIHN0cm9rZTogXCJjdXJyZW50Q29sb3JcIixcbiAgICBcImFyaWEtaGlkZGVuXCI6IFwidHJ1ZVwiLFxuICAgIFwiZGF0YS1zbG90XCI6IFwiaWNvblwiLFxuICAgIHJlZjogc3ZnUmVmLFxuICAgIFwiYXJpYS1sYWJlbGxlZGJ5XCI6IHRpdGxlSWRcbiAgfSwgcHJvcHMpLCB0aXRsZSA/IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwidGl0bGVcIiwge1xuICAgIGlkOiB0aXRsZUlkXG4gIH0sIHRpdGxlKSA6IG51bGwsIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwicGF0aFwiLCB7XG4gICAgc3Ryb2tlTGluZWNhcDogXCJyb3VuZFwiLFxuICAgIHN0cm9rZUxpbmVqb2luOiBcInJvdW5kXCIsXG4gICAgZDogXCJNMjAuMjUgNi4zNzVjMCAyLjI3OC0zLjY5NCA0LjEyNS04LjI1IDQuMTI1UzMuNzUgOC42NTMgMy43NSA2LjM3NW0xNi41IDBjMC0yLjI3OC0zLjY5NC00LjEyNS04LjI1LTQuMTI1UzMuNzUgNC4wOTcgMy43NSA2LjM3NW0xNi41IDB2MTEuMjVjMCAyLjI3OC0zLjY5NCA0LjEyNS04LjI1IDQuMTI1cy04LjI1LTEuODQ3LTguMjUtNC4xMjVWNi4zNzVtMTYuNSAwdjMuNzVtLTE2LjUtMy43NXYzLjc1bTE2LjUgMHYzLjc1QzIwLjI1IDE2LjE1MyAxNi41NTYgMTggMTIgMThzLTguMjUtMS44NDctOC4yNS00LjEyNXYtMy43NW0xNi41IDBjMCAyLjI3OC0zLjY5NCA0LjEyNS04LjI1IDQuMTI1cy04LjI1LTEuODQ3LTguMjUtNC4xMjVcIlxuICB9KSk7XG59XG5jb25zdCBGb3J3YXJkUmVmID0gLyojX19QVVJFX18qLyBSZWFjdC5mb3J3YXJkUmVmKENpcmNsZVN0YWNrSWNvbik7XG5leHBvcnQgZGVmYXVsdCBGb3J3YXJkUmVmOyJdLCJuYW1lcyI6WyJSZWFjdCIsIkNpcmNsZVN0YWNrSWNvbiIsInN2Z1JlZiIsInRpdGxlIiwidGl0bGVJZCIsInByb3BzIiwiY3JlYXRlRWxlbWVudCIsIk9iamVjdCIsImFzc2lnbiIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2VXaWR0aCIsInN0cm9rZSIsInJlZiIsImlkIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwiZCIsIkZvcndhcmRSZWYiLCJmb3J3YXJkUmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\n"));

/***/ })

});