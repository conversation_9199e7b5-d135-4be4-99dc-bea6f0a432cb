"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/EnergyStats.tsx":
/*!****************************************!*\
  !*** ./src/components/EnergyStats.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EnergyStats; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CurrencyDollarIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SunIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CurrencyDollarIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CurrencyDollarIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CurrencyDollarIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CurrencyDollarIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _store_energyStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/energyStore */ \"(app-pages-browser)/./src/store/energyStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction EnergyStats() {\n    _s();\n    const { energyData } = (0,_store_energyStore__WEBPACK_IMPORTED_MODULE_1__.useEnergyStore)();\n    const stats = [\n        {\n            name: \"Total Production\",\n            value: \"\".concat(energyData.totalProduction.toFixed(1), \" kWh\"),\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: \"text-energy-production\",\n            bgColor: \"bg-green-50\",\n            change: \"+12.5%\",\n            changeType: \"increase\"\n        },\n        {\n            name: \"Total Consumption\",\n            value: \"\".concat(energyData.totalConsumption.toFixed(1), \" kWh\"),\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"text-energy-consumption\",\n            bgColor: \"bg-yellow-50\",\n            change: \"-3.2%\",\n            changeType: \"decrease\"\n        },\n        {\n            name: \"Current Balance\",\n            value: \"\".concat(energyData.currentBalance.toFixed(1), \" kWh\"),\n            icon: BatteryIcon,\n            color: \"text-energy-trading\",\n            bgColor: \"bg-purple-50\",\n            change: \"+8.7%\",\n            changeType: \"increase\"\n        },\n        {\n            name: \"Credits Earned\",\n            value: \"\".concat(energyData.creditsEarned.toLocaleString(), \" EC\"),\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"text-primary-600\",\n            bgColor: \"bg-blue-50\",\n            change: \"+15.3%\",\n            changeType: \"increase\"\n        },\n        {\n            name: \"Credits Spent\",\n            value: \"\".concat(energyData.creditsSpent.toLocaleString(), \" EC\"),\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"text-gray-600\",\n            bgColor: \"bg-gray-50\",\n            change: \"+5.1%\",\n            changeType: \"increase\"\n        },\n        {\n            name: \"Carbon Offset\",\n            value: \"\".concat(energyData.carbonOffset.toFixed(1), \" kg CO₂\"),\n            icon: LeafIcon,\n            color: \"text-green-600\",\n            bgColor: \"bg-green-50\",\n            change: \"+18.9%\",\n            changeType: \"increase\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                children: \"Energy Overview\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: stats.map((stat)=>{\n                    const Icon = stat.icon;\n                    const ChangeIcon = stat.changeType === \"increase\" ? _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"] : _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CurrencyDollarIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"stat-card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 p-3 rounded-lg \".concat(stat.bgColor),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-6 w-6 \".concat(stat.color)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: stat.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChangeIcon, {\n                                        className: \"h-4 w-4 \".concat(stat.changeType === \"increase\" ? \"text-green-500\" : \"text-red-500\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-1 text-sm font-medium \".concat(stat.changeType === \"increase\" ? \"text-green-600\" : \"text-red-600\"),\n                                        children: stat.change\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm text-gray-500\",\n                                        children: \"vs last month\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, stat.name, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n_s(EnergyStats, \"pUOaHFdu8q23rBXuo08J6hGBD6U=\", false, function() {\n    return [\n        _store_energyStore__WEBPACK_IMPORTED_MODULE_1__.useEnergyStore\n    ];\n});\n_c = EnergyStats;\nvar _c;\n$RefreshReg$(_c, \"EnergyStats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EnergyStats.tsx\n"));

/***/ })

});