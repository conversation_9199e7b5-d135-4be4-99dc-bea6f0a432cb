"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/EnergyStats.tsx":
/*!****************************************!*\
  !*** ./src/components/EnergyStats.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EnergyStats; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CircleStackIcon,CurrencyDollarIcon,GlobeAltIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SunIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CircleStackIcon,CurrencyDollarIcon,GlobeAltIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CircleStackIcon,CurrencyDollarIcon,GlobeAltIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CircleStackIcon,CurrencyDollarIcon,GlobeAltIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CircleStackIcon,CurrencyDollarIcon,GlobeAltIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CircleStackIcon,CurrencyDollarIcon,GlobeAltIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,BoltIcon,CircleStackIcon,CurrencyDollarIcon,GlobeAltIcon,SunIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _store_energyStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/energyStore */ \"(app-pages-browser)/./src/store/energyStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction EnergyStats() {\n    _s();\n    const { energyData } = (0,_store_energyStore__WEBPACK_IMPORTED_MODULE_1__.useEnergyStore)();\n    const stats = [\n        {\n            name: \"Total Production\",\n            value: \"\".concat(energyData.totalProduction.toFixed(1), \" kWh\"),\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: \"text-energy-production\",\n            bgColor: \"bg-green-50\",\n            change: \"+12.5%\",\n            changeType: \"increase\"\n        },\n        {\n            name: \"Total Consumption\",\n            value: \"\".concat(energyData.totalConsumption.toFixed(1), \" kWh\"),\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"text-energy-consumption\",\n            bgColor: \"bg-yellow-50\",\n            change: \"-3.2%\",\n            changeType: \"decrease\"\n        },\n        {\n            name: \"Current Balance\",\n            value: \"\".concat(energyData.currentBalance.toFixed(1), \" kWh\"),\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"text-energy-trading\",\n            bgColor: \"bg-purple-50\",\n            change: \"+8.7%\",\n            changeType: \"increase\"\n        },\n        {\n            name: \"Credits Earned\",\n            value: \"\".concat(energyData.creditsEarned.toLocaleString(), \" EC\"),\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"text-primary-600\",\n            bgColor: \"bg-blue-50\",\n            change: \"+15.3%\",\n            changeType: \"increase\"\n        },\n        {\n            name: \"Credits Spent\",\n            value: \"\".concat(energyData.creditsSpent.toLocaleString(), \" EC\"),\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"text-gray-600\",\n            bgColor: \"bg-gray-50\",\n            change: \"+5.1%\",\n            changeType: \"increase\"\n        },\n        {\n            name: \"Carbon Offset\",\n            value: \"\".concat(energyData.carbonOffset.toFixed(1), \" kg CO₂\"),\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"text-green-600\",\n            bgColor: \"bg-green-50\",\n            change: \"+18.9%\",\n            changeType: \"increase\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                children: \"Energy Overview\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: stats.map((stat)=>{\n                    const Icon = stat.icon;\n                    const ChangeIcon = stat.changeType === \"increase\" ? _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"] : _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_BoltIcon_CircleStackIcon_CurrencyDollarIcon_GlobeAltIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"stat-card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 p-3 rounded-lg \".concat(stat.bgColor),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-6 w-6 \".concat(stat.color)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: stat.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChangeIcon, {\n                                        className: \"h-4 w-4 \".concat(stat.changeType === \"increase\" ? \"text-green-500\" : \"text-red-500\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-1 text-sm font-medium \".concat(stat.changeType === \"increase\" ? \"text-green-600\" : \"text-red-600\"),\n                                        children: stat.change\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm text-gray-500\",\n                                        children: \"vs last month\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, stat.name, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/augment/maschain/src/components/EnergyStats.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n_s(EnergyStats, \"pUOaHFdu8q23rBXuo08J6hGBD6U=\", false, function() {\n    return [\n        _store_energyStore__WEBPACK_IMPORTED_MODULE_1__.useEnergyStore\n    ];\n});\n_c = EnergyStats;\nvar _c;\n$RefreshReg$(_c, \"EnergyStats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EnergyStats.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction GlobeAltIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418\"\n    }));\n}\n_c = GlobeAltIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(GlobeAltIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"GlobeAltIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\n"));

/***/ })

});