{"name": "maschain-energy-backend", "version": "0.1.0", "description": "Backend services for Maschain Energy Trading Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "@solana/web3.js": "^1.87.6", "@project-serum/anchor": "^0.28.0", "ws": "^8.14.2", "redis": "^4.6.10", "pg": "^8.11.3", "mongoose": "^7.6.3", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "node-cron": "^3.0.2", "axios": "^1.5.1"}, "devDependencies": {"@types/node": "^20.8.0", "@types/express": "^4.17.20", "@types/cors": "^2.8.15", "@types/morgan": "^1.9.7", "@types/ws": "^8.5.8", "@types/pg": "^8.10.7", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.4", "@types/bcryptjs": "^2.4.5", "@types/node-cron": "^3.0.9", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.7.0", "@types/jest": "^29.5.6", "ts-jest": "^29.1.1", "eslint": "^8.51.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}